<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5订单详情页-取消时间功能原型</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7f8fa;
            color: #323233;
            line-height: 1.4;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background-color: #fff;
            min-height: 100vh;
        }
        
        /* 头部信息区域 */
        .order-header {
            padding: 16px;
            background-color: #fff;
            border-bottom: 1px solid #ebedf0;
        }
        
        .order-status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .channel-info {
            font-size: 14px;
            color: #646566;
        }
        
        .status-badge {
            background-color: #ff976a;
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .cancel-policy-btn {
            background: none;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        
        /* 订单信息列表 */
        .order-info-list {
            list-style: none;
        }
        
        .order-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            font-size: 14px;
            border-bottom: 1px solid #f7f8fa;
        }
        
        .order-info-item:last-child {
            border-bottom: none;
        }
        
        .order-info-label {
            color: #646566;
            min-width: 60px;
        }
        
        .order-info-value {
            flex: 1;
            text-align: right;
            color: #323233;
        }
        
        .copy-btn {
            background: none;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-left: 8px;
            cursor: pointer;
        }
        
        /* 变更高亮样式 */
        .change-highlight-new {
            background-color: #e8f5e8 !important;
            border: 2px solid #52c41a !important;
            border-radius: 4px;
            position: relative;
            animation: highlight-pulse 2s ease-in-out infinite;
        }
        
        .change-highlight-new::before {
            content: "新增";
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #52c41a;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: bold;
        }
        
        @keyframes highlight-pulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4); }
            50% { box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.1); }
        }
        
        /* 标签样式 */
        .tag {
            display: inline-block;
            background-color: #f2f3f5;
            color: #646566;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 8px;
        }
        
        /* 金额区域 */
        .amount-section {
            padding: 16px;
            background-color: #fff;
            margin-top: 8px;
            border-bottom: 1px solid #ebedf0;
        }
        
        .amount-header {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 12px;
        }
        
        .amount-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .amount-details {
            font-size: 14px;
        }
        
        .amount-total {
            color: #323233;
            margin-bottom: 4px;
        }
        
        .amount-paid {
            color: #646566;
        }
        
        .detail-btn {
            background: none;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 订单头部信息 -->
        <div class="order-header">
            <div class="order-status-bar">
                <span class="channel-info">携程</span>
                <span class="status-badge">已取消</span>
                <button class="cancel-policy-btn">取消政策</button>
            </div>
            
            <ul class="order-info-list">
                <li class="order-info-item">
                    <span class="order-info-label">下单：</span>
                    <span class="order-info-value">2025-07-31 13:30</span>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">用车：</span>
                    <span class="order-info-value">2025-08-02 10:00 至 2025-08-03 10:00, 1天</span>
                </li>
                <!-- 新增：取消时间字段 -->
                <li class="order-info-item change-highlight-new">
                    <span class="order-info-label">取消：</span>
                    <span class="order-info-value">2025-07-31 15:45</span>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">订单号：</span>
                    <span class="order-info-value">
                        1706192
                        <button class="copy-btn">复制</button>
                    </span>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">渠道订单号：</span>
                    <span class="order-info-value">
                        1128168994656811
                        <button class="copy-btn">复制</button>
                    </span>
                </li>
            </ul>
            
            <div style="margin-top: 12px;">
                <span class="tag">押金未收</span>
                <span class="tag">手续费</span>
                <span class="tag">两年以上车龄</span>
            </div>
        </div>
        
        <!-- 订单金额 -->
        <div class="amount-section">
            <div class="amount-header">订单金额</div>
            <div class="amount-summary">
                <div class="amount-details">
                    <div class="amount-total">应收合计560.00元</div>
                    <div class="amount-paid">实收合计550.00元</div>
                </div>
                <button class="detail-btn">费用明细</button>
            </div>
        </div>
        
        <!-- 说明文档 -->
        <div style="padding: 20px; background-color: #f7f8fa; margin-top: 20px; border-radius: 8px; margin: 20px 16px;">
            <h3 style="color: #1989fa; margin-bottom: 12px;">📋 功能说明</h3>
            <div style="font-size: 14px; line-height: 1.6; color: #646566;">
                <p><strong>新增功能：</strong>取消时间显示</p>
                <p><strong>显示位置：</strong>订单信息列表中，位于"用车"时间之后</p>
                <p><strong>显示格式：</strong>yyyy-MM-dd hh:mm（如：2025-07-31 15:45）</p>
                <p><strong>显示条件：</strong>仅在订单状态为"已取消"时显示</p>
                <p><strong>数据来源：</strong>API返回的cancelTime字段（时间戳格式，秒）</p>
                <p><strong>技术实现：</strong>使用Tempo.format()工具进行时间格式化</p>
            </div>
        </div>
    </div>
</body>
</html>
