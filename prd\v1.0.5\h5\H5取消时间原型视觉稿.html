<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5订单详情页-取消时间功能完整原型</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7f8fa;
            color: #323233;
            line-height: 1.4;
            font-size: 14px;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background-color: #f7f8fa;
            min-height: 100vh;
        }

        /* 页面区块通用样式 */
        .section {
            background-color: #fff;
            margin-bottom: 8px;
            padding: 16px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #323233;
            margin-bottom: 12px;
        }

        /* 头部信息区域 */
        .order-header {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .order-status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .channel-info {
            font-size: 14px;
            color: #646566;
        }

        .status-badge {
            background-color: #ff976a;
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .cancel-policy-btn {
            background: none;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        /* 订单信息列表 */
        .order-info-list {
            list-style: none;
            margin-bottom: 12px;
        }

        .order-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            font-size: 14px;
            border-bottom: 1px solid #f7f8fa;
        }

        .order-info-item:last-child {
            border-bottom: none;
        }

        .order-info-label {
            color: #646566;
            min-width: 60px;
        }

        .order-info-value {
            flex: 1;
            text-align: right;
            color: #323233;
        }

        .copy-btn {
            background: none;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-left: 8px;
            cursor: pointer;
        }

        /* 变更高亮样式 */
        .change-highlight-new {
            background-color: #e8f5e8 !important;
            border: 2px solid #52c41a !important;
            border-radius: 4px;
            position: relative;
            animation: highlight-pulse 2s ease-in-out infinite;
            margin: 0 -8px;
            padding: 8px 8px;
        }

        .change-highlight-new::before {
            content: "新增";
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #52c41a;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: bold;
        }

        @keyframes highlight-pulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4); }
            50% { box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.1); }
        }

        /* 标签样式 */
        .tag {
            display: inline-block;
            background-color: #f2f3f5;
            color: #646566;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 8px;
        }

        /* 金额区域 */
        .amount-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .amount-details {
            font-size: 14px;
        }

        .amount-total {
            color: #323233;
            margin-bottom: 4px;
        }

        .amount-paid {
            color: #646566;
        }

        .detail-btn {
            background: none;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        /* 押金区域 */
        .deposit-info {
            font-size: 14px;
            color: #323233;
            margin-bottom: 8px;
        }

        .deposit-item {
            margin-bottom: 4px;
        }

        /* 记录按钮区域 */
        .records-section {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .record-btn {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f7f8fa;
            background: none;
            border: none;
            width: 100%;
            cursor: pointer;
            font-size: 14px;
        }

        .record-btn:last-child {
            border-bottom: none;
        }

        .record-btn-left {
            display: flex;
            align-items: center;
        }

        .record-btn-title {
            color: #323233;
            margin-right: 8px;
        }

        .record-btn-subtitle {
            color: #646566;
            font-size: 12px;
        }

        .record-btn-count {
            color: #646566;
            font-size: 12px;
        }

        .record-btn-arrow {
            color: #c8c9cc;
            font-size: 12px;
        }

        /* 承租人信息 */
        .tenant-info {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .tenant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .tenant-fields {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }

        .tenant-field {
            text-align: center;
        }

        .tenant-field-label {
            color: #646566;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .tenant-field-value {
            color: #323233;
            font-size: 14px;
        }

        /* 车辆信息 */
        .vehicle-info {
            background-color: #fff;
            padding: 16px;
            margin-bottom: 8px;
        }

        .vehicle-title {
            color: #323233;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .vehicle-subtitle {
            color: #646566;
            font-size: 12px;
            margin-bottom: 12px;
        }

        .pickup-return-info {
            margin-bottom: 16px;
        }

        .pickup-return-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .pickup-return-label {
            color: #646566;
            font-size: 12px;
            min-width: 60px;
        }

        .pickup-return-value {
            flex: 1;
            color: #323233;
            font-size: 12px;
            text-align: right;
        }

        .map-link {
            color: #1989fa;
            text-decoration: none;
            font-size: 12px;
        }

        /* 服务表格 */
        .service-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 8px;
        }

        .service-table th {
            background-color: #f7f8fa;
            color: #646566;
            font-size: 12px;
            font-weight: normal;
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ebedf0;
        }

        .service-table td {
            padding: 12px 8px;
            font-size: 12px;
            border-bottom: 1px solid #f7f8fa;
        }

        .service-name {
            color: #323233;
        }

        .service-status {
            color: #52c41a;
        }

        .service-price {
            color: #323233;
            text-align: right;
        }

        .service-link {
            color: #1989fa;
            text-decoration: none;
        }

        /* 底部更多按钮 */
        .more-section {
            background-color: #fff;
            padding: 16px;
            text-align: center;
        }

        .more-btn {
            background: none;
            border: 1px solid #1989fa;
            color: #1989fa;
            padding: 8px 24px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 订单头部信息 -->
        <div class="order-header">
            <div class="order-status-bar">
                <span class="channel-info">携程</span>
                <span class="status-badge">已取消</span>
                <button class="cancel-policy-btn">取消政策</button>
            </div>

            <ul class="order-info-list">
                <li class="order-info-item">
                    <span class="order-info-label">下单：</span>
                    <span class="order-info-value">2025-07-31 13:30</span>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">用车：</span>
                    <span class="order-info-value">2025-08-02 10:00 至 2025-08-03 10:00, 1天</span>
                </li>
                <!-- 新增：取消时间字段 -->
                <li class="order-info-item change-highlight-new">
                    <span class="order-info-label">取消：</span>
                    <span class="order-info-value">2025-07-31 15:45</span>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">订单号：</span>
                    <span class="order-info-value">
                        1706192
                        <button class="copy-btn">复制</button>
                    </span>
                </li>
                <li class="order-info-item">
                    <span class="order-info-label">渠道订单号：</span>
                    <span class="order-info-value">
                        1128168994656811
                        <button class="copy-btn">复制</button>
                    </span>
                </li>
            </ul>

            <div>
                <span class="tag">押金未收</span>
                <span class="tag">手续费</span>
                <span class="tag">两年以上车龄</span>
            </div>
        </div>

        <!-- 订单金额 -->
        <div class="section">
            <div class="section-title">订单金额</div>
            <div class="amount-summary">
                <div class="amount-details">
                    <div class="amount-total">应收合计560.00元</div>
                    <div class="amount-paid">实收合计550.00元</div>
                </div>
                <button class="detail-btn">费用明细</button>
            </div>
        </div>

        <!-- 押金 -->
        <div class="section">
            <div class="section-title">押金</div>
            <div class="deposit-info">
                <div class="deposit-item">租车押金2000元</div>
                <div class="deposit-item">违章押金500元</div>
            </div>
            <button class="detail-btn">押金政策</button>
        </div>

        <!-- 记录按钮区域 -->
        <div class="records-section">
            <button class="record-btn">
                <div class="record-btn-left">
                    <span class="record-btn-title">续租记录</span>
                    <span class="record-btn-count">0笔</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
            <button class="record-btn">
                <div class="record-btn-left">
                    <span class="record-btn-title">取还车记录</span>
                    <span class="record-btn-subtitle">取还车信息及租车单据</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
            <button class="record-btn">
                <div class="record-btn-left">
                    <span class="record-btn-title">车损记录</span>
                    <span class="record-btn-count">0笔</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
            <button class="record-btn">
                <div class="record-btn-left">
                    <span class="record-btn-title">违章记录</span>
                    <span class="record-btn-count">0笔</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
            <button class="record-btn">
                <div class="record-btn-left">
                    <span class="record-btn-title">订单备注</span>
                    <span class="record-btn-count">0条</span>
                </div>
                <span class="record-btn-arrow">></span>
            </button>
        </div>

        <!-- 承租人信息 -->
        <div class="tenant-info">
            <div class="tenant-header">
                <div class="section-title">承租人信息</div>
                <button class="detail-btn">录入证件信息</button>
            </div>
            <div class="tenant-fields">
                <div class="tenant-field">
                    <div class="tenant-field-label">姓名</div>
                    <div class="tenant-field-value">礼品卡</div>
                </div>
                <div class="tenant-field">
                    <div class="tenant-field-label">证件号</div>
                    <div class="tenant-field-value">身份证 321282199111053247</div>
                </div>
                <div class="tenant-field">
                    <div class="tenant-field-label">手机号</div>
                    <div class="tenant-field-value">13162779073</div>
                </div>
            </div>
        </div>

        <!-- 车辆信息 -->
        <div class="vehicle-info">
            <div class="vehicle-title">豪华型 1492-奥迪 A6L 2023款 40 TFSI 豪华动感型 自助取还版 有天窗 沪ACK9992</div>
            <div class="vehicle-subtitle">用户预定车型 1492-奥迪 A6L 2023款 40 TFSI 豪华动感型 有天窗 普牌</div>
            <div class="tag">两年以上车龄</div>

            <div class="pickup-return-info">
                <div class="pickup-return-row">
                    <span class="pickup-return-label">预计取车</span>
                    <span class="pickup-return-value">2025-08-02 10:00</span>
                </div>
                <div class="pickup-return-row">
                    <span class="pickup-return-label">取车门店</span>
                    <span class="pickup-return-value">三亚店 上门送车</span>
                </div>
                <div class="pickup-return-row">
                    <span class="pickup-return-label">取车地址</span>
                    <span class="pickup-return-value">
                        凤凰国际机场-国际航站楼
                        <a href="#" class="map-link">查看地图</a>
                    </span>
                </div>
            </div>

            <div class="pickup-return-info">
                <div class="pickup-return-row">
                    <span class="pickup-return-label">预计还车</span>
                    <span class="pickup-return-value">2025-08-03 10:00</span>
                </div>
                <div class="pickup-return-row">
                    <span class="pickup-return-label">还车门店</span>
                    <span class="pickup-return-value">三亚店 上门取车</span>
                </div>
                <div class="pickup-return-row">
                    <span class="pickup-return-label">还车地址</span>
                    <span class="pickup-return-value">
                        凤凰国际机场-国际航站楼
                        <a href="#" class="map-link">查看地图</a>
                    </span>
                </div>
            </div>
        </div>

        <!-- 保险 -->
        <div class="section">
            <div class="section-title">保险</div>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>状态</th>
                        <th>总价</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="service-name">
                            基本保障服务费
                            <a href="#" class="service-link">详情</a>
                        </td>
                        <td class="service-status">全额退款</td>
                        <td class="service-price">￥40.00</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 附加服务 -->
        <div class="section">
            <div class="section-title">附加服务</div>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>状态</th>
                        <th>总价</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="service-name">手续费</td>
                        <td class="service-status">全额退款</td>
                        <td class="service-price">￥20.00</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 底部更多按钮 -->
        <div class="more-section">
            <button class="more-btn">更多</button>
        </div>

        <!-- 功能说明文档 -->
        <div style="padding: 20px; background-color: #fff3cd; margin: 16px; border-radius: 8px; border: 1px solid #ffeaa7;">
            <h3 style="color: #856404; margin-bottom: 12px;">📋 原型说明</h3>
            <div style="font-size: 12px; line-height: 1.6; color: #856404;">
                <p><strong>✨ 新增功能：</strong>取消时间显示字段</p>
                <p><strong>📍 显示位置：</strong>订单信息列表中，位于"用车"时间之后</p>
                <p><strong>🎯 显示条件：</strong>仅在订单状态为"已取消"时显示</p>
                <p><strong>📅 时间格式：</strong>yyyy-MM-dd hh:mm（示例：2025-07-31 15:45）</p>
                <p><strong>🔧 技术实现：</strong>使用Tempo.format()工具格式化cancelTime字段</p>
                <p><strong>🎨 设计规范：</strong>与现有时间字段保持完全一致的样式</p>
                <p><strong>💡 高亮说明：</strong>绿色边框和"新增"标签仅用于原型演示，实际上线时移除</p>
            </div>
        </div>
    </div>
</body>
</html>
