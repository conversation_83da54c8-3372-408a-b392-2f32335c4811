# H5取消时间功能变更文档

## 1. 功能概述

### 1.1 需求描述
在H5短租订单详情页面中，为已取消的订单添加"取消时间"显示功能，让用户能够清楚地看到订单的取消时间。

### 1.2 功能目标
- 提升用户体验，让用户了解订单取消的具体时间
- 保持与现有设计风格的一致性
- 仅在已取消订单中显示，避免信息冗余

## 2. 技术实现方案

### 2.1 前端实现

#### 2.1.1 文件修改清单
```
modules/h5/views/order/detail.vue
modules/h5/comp-lib/qinglu-vant/src/services/models/OrderDetailModels/OrderDetailInfoModel.js
```

#### 2.1.2 数据模型扩展
**文件：** `OrderDetailInfoModel.js`

**新增字段：**
```javascript
// 在OrderDetailInfoModel类中添加
cancelTimeStr = {
  type: String,
  field: (data) => {
    // 仅在订单状态为已取消时返回格式化时间
    if (data.status === 'CANCELLED' && data.cancelTime) {
      return Tempo.format(data.cancelTime);
    }
    return null;
  }
}
```

**说明：**
- 使用现有的Tempo工具进行时间格式化
- 默认格式为'yyyy-MM-dd hh:mm'
- 仅在订单状态为已取消且存在cancelTime时返回值

#### 2.1.3 视图层修改
**文件：** `detail.vue`

**模板修改：**
```vue
<template>
  <!-- 现有订单信息列表 -->
  <van-cell-group>
    <van-cell title="下单：" :value="orderInfo.orderTimeStr" />
    <van-cell title="用车：" :value="orderInfo.useTimeStr" />
    
    <!-- 新增：取消时间显示 -->
    <van-cell 
      v-if="orderInfo.cancelTimeStr" 
      title="取消：" 
      :value="orderInfo.cancelTimeStr" 
      class="cancel-time-cell"
    />
    
    <van-cell title="订单号：" :value="orderInfo.orderNo">
      <template #right-icon>
        <van-button size="mini" @click="copyOrderNo">复制</van-button>
      </template>
    </van-cell>
    <!-- 其他现有字段... -->
  </van-cell-group>
</template>
```

**样式修改：**
```vue
<style scoped>
.cancel-time-cell {
  /* 保持与现有样式一致 */
  background-color: #fff;
}

/* 如需特殊标识可添加 */
.cancel-time-cell .van-cell__title {
  color: #ff976a; /* 与取消状态颜色保持一致 */
}
</style>
```

### 2.2 后端API扩展

#### 2.2.1 API响应字段
**接口：** 订单详情API

**新增响应字段：**
```json
{
  "data": {
    "orderNo": "1706192",
    "status": "CANCELLED",
    "orderTime": 1722408600,
    "cancelTime": 1722416700,  // 新增：取消时间戳（秒）
    // ... 其他现有字段
  }
}
```

**字段说明：**
- `cancelTime`: 订单取消时间戳，单位为秒
- 仅在订单状态为已取消时返回此字段
- 时间戳格式便于前端进行时区转换和格式化

## 3. 设计规范

### 3.1 显示位置
- 位置：订单信息列表中，"用车"时间字段之后
- 顺序：下单 → 用车 → **取消** → 订单号 → 渠道订单号

### 3.2 显示格式
- 标签：`取消：`
- 时间格式：`yyyy-MM-dd hh:mm`
- 示例：`取消：2025-07-31 15:45`

### 3.3 显示条件
- 仅在订单状态为"已取消"时显示
- 必须存在有效的cancelTime数据
- 与现有时间字段保持相同的样式风格

### 3.4 样式规范
- 字体大小：14px（与现有信息字段一致）
- 颜色：标题使用#646566，内容使用#323233
- 布局：左右对齐，与现有字段保持一致
- 间距：上下8px padding

## 4. 测试要点

### 4.1 功能测试
- [ ] 已取消订单显示取消时间
- [ ] 非取消订单不显示取消时间
- [ ] 时间格式正确显示
- [ ] 时间数据为空时不显示

### 4.2 兼容性测试
- [ ] iOS Safari浏览器
- [ ] Android Chrome浏览器
- [ ] 微信内置浏览器
- [ ] 不同屏幕尺寸适配

### 4.3 性能测试
- [ ] 页面加载时间无明显增加
- [ ] 时间格式化性能正常

## 5. 上线计划

### 5.1 开发阶段
1. 后端API扩展（1天）
2. 前端数据模型修改（0.5天）
3. 前端视图层实现（0.5天）
4. 联调测试（1天）

### 5.2 测试阶段
1. 功能测试（1天）
2. 兼容性测试（1天）
3. 回归测试（0.5天）

### 5.3 发布阶段
1. 预发布环境验证（0.5天）
2. 生产环境发布（0.5天）

**总计开发周期：5.5天**

## 6. 风险评估

### 6.1 技术风险
- **低风险**：使用现有技术栈和工具
- **低风险**：修改范围小，影响面可控

### 6.2 业务风险
- **低风险**：纯展示功能，不影响核心业务流程
- **低风险**：向下兼容，不影响现有功能

### 6.3 用户体验风险
- **低风险**：信息增加有助于用户理解订单状态
- **低风险**：设计保持一致性，不会造成困惑

## 7. 验收标准

### 7.1 功能验收
- ✅ 已取消订单正确显示取消时间
- ✅ 时间格式符合设计规范
- ✅ 显示位置正确
- ✅ 非取消订单不显示该字段

### 7.2 性能验收
- ✅ 页面加载时间无明显增加
- ✅ 内存使用无异常增长

### 7.3 兼容性验收
- ✅ 主流移动端浏览器正常显示
- ✅ 不同屏幕尺寸适配良好

---

**文档版本：** v1.0  
**创建日期：** 2025-07-31  
**最后更新：** 2025-07-31  
**负责人：** 产品开发团队
